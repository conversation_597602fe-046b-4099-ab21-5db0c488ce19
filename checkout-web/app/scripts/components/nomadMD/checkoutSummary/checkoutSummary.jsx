import React, { useEffect, useRef, useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';

import Events from '../../../events';
import Session from '../../../session';
import Divider from '../divider/divider';
import ToastSystem from '../../../toast';
import SummaryNotes from './summaryNotes';
import SummaryTotal from './summaryTotal';
import bemify from '../../../utils/bemUtils';
import SummaryContact from './summaryContact';
import SummaryPayment from './summaryPayment';
import SummaryBooking from './summaryBooking';
import SummaryGratuity from './summaryGratuity';
import Button from '../../controls/button/button';
import SummaryMembership from './summaryMembership';
import SummaryPaymentPolicy from './summaryPaymentPolicy';
import SpinnerLoader from '../../loader/spinnerLoader';
import { useSession } from '../../../hooks/useStorage';
import { getFormData } from '../../../utils/formUtils';
import NomadMDService from '../../../services/nomadService';
import formsSummary from '../../../data/formPersonalInformation.json';
import { EventMessages, SessionStorageKeys } from '../../../constants';
import { isLoadingMutation } from '../../../utils/networkUtils';
import { useAppNavigate } from '../../../hooks/useAppNavigate';
import ErrorBar from '../errorBar/errorBar';

import { getAppConfig, mergeMembershipDefinitions, parseAvailabilityData } from '../../../utils/nomadMD';

/**
 * Represents the checkout summary.
 */
const CheckoutSummary = () => {
  const { location, appointmentType } = getAppConfig();

  const navigate = useAppNavigate();

  const summaryPaymentRef = useRef(null);

  const [showCompleteCheckout, setShowCompleteCheckout] = useState(false);

  const [contactData, setContactData] = useState({});
  const [errors, setErrors] = useState([]);

  /** @type {Clinic?} */
  const organizationSelected = useSession({
    key: SessionStorageKeys.location
  });

  /** @type {Availability} */
  const selectedAppointment = useSession({
    key: SessionStorageKeys.appointment,
    defaultValue: [],
    onMissing: () => {
      Events.emit(EventMessages.modalNavigationError, true);
    }
  });

  /** @type {VirtualAvailability[]} */
  const selectedAvailabilities = useSession({
    //reset: true,
    key: SessionStorageKeys.availabilities,
    defaultValue: []
  });

  /** @type {Array<ProcedureWithAddOns | ProcedureAddOn> } */
  const procedures = useSession({
    defaultValue: [],
    key: SessionStorageKeys.procedures,
    onMissing: () => {
      Events.emit(EventMessages.modalNavigationError, true);
    }
  });

  /** @type {User?} */
  const userSession = useSession({
    key: SessionStorageKeys.user
  });

  const [userHasMembership, setUserHasMembership] = useState(false);

  /** @type {[PaymentInstrument?, Function]} */
  const [selectedPaymentInstrument, setSelectedPaymentInstrument] = useState();

  /** @type {[(MembershipDefinition)?, Function]} */
  const [selectedMembership, setSelectedMembership] = useState();

  /** @type {[{ id: number, value: number, selected: boolean }?, Function]} */
  const [selectedGratuity, setSelectedGratuity] = useState();

  /** @type {[{ token: string, identity: object }?, Function]} */
  const [paymentToken, setPaymentToken] = useState();

  /** @type {[boolean, Function]} */
  const [paymentPolicyAttested, setPaymentPolicyAttested] = useState(false);

  const marketPlaceQuery = useQuery({
    queryKey: NomadMDService.getMarketplace.key(),
    queryFn: () => NomadMDService.getMarketplace.function()
  });

  const userQuery = useQuery({
    gcTime: 0,
    staleTime: 0,
    refetchOnMount: true,
    enabled: !!userSession?.id,
    queryKey: NomadMDService.getUser.key(),
    queryFn: () => NomadMDService.getUser.function()
  });

  const userMembershipsQuery = useQuery({
    gcTime: 0,
    staleTime: 0,
    refetchOnMount: true,
    networkMode: 'always',
    enabled: Boolean(
      (userQuery.data?.memberships || userSession?.memberships) && marketPlaceQuery.data?.membershipDefinitions
    ),
    queryKey: [userQuery.data?.memberships || userSession?.memberships, marketPlaceQuery.data?.membershipDefinitions],
    queryFn: () =>
      mergeMembershipDefinitions(
        userQuery.data?.memberships || userSession?.memberships,
        marketPlaceQuery.data?.membershipDefinitions
      )
  });

  // Calculate gratuity for the checkout summary query
  const calculateGratuityAmount = () => {
    if (!selectedGratuity) return 0;

    const proceduresTotal = procedures.reduce((total, procedure) => total + Math.min(...procedure.price), 0);

    if (selectedGratuity.id !== 'other') {
      // For percentage-based gratuity, calculate in cents and round to nearest integer
      const percentage = (selectedGratuity.value ?? 0) / 100;
      return Math.round(percentage * proceduresTotal * 100);
    } else {
      // For custom, assume value is in dollars, convert to cents and round
      return Math.round((selectedGratuity.value || 0) * 100);
    }
  };

  const checkoutSummaryQuery = useQuery({
    enabled: Boolean(procedures?.length),
    queryKey: NomadMDService.getCheckoutSummary.key(
      procedures,
      location,
      organizationSelected?.id,
      selectedMembership?.id,
      selectedGratuity
    ),
    queryFn: () =>
      NomadMDService.getCheckoutSummary.function({
        procedures,
        address: appointmentType === 'home' ? location?.formattedAddress : undefined,
        organizationId: appointmentType === 'clinic' ? organizationSelected?.id : undefined,
        membershipDefinitionId: !userHasMembership ? selectedMembership?.id : undefined,
        gratuity: calculateGratuityAmount() / 100
      })
  });

  const createAppointmentMutation = useMutation({
    mutationFn: NomadMDService.createAppointment.function,
    onSuccess: () => {
      ToastSystem.show('Appointment created successfully', {
        type: 'success'
      });

      Session.setItem(SessionStorageKeys.step, 0);
      Session.setItem(SessionStorageKeys.services, []);
      Session.setItem(SessionStorageKeys.procedures, []);
      Session.setItem(SessionStorageKeys.appointment, null);
      Session.setItem(SessionStorageKeys.availabilities, []);

      Session.setItem(SessionStorageKeys.accountManagementTab, 'appointments');
      navigate('/success');
    },
    onError: error => {
      setErrors([error?.message || 'Failed to create appointment. Please try again.']);
    }
  });

  useEffect(() => {
    if (userQuery?.data) {
      Session.setItem(SessionStorageKeys.user, userQuery.data);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userQuery?.dataUpdatedAt]);

  useEffect(() => {
    if (userSession?.primaryInstrumentId) {
      const paymentInstrument = userSession.paymentInstruments.find(i => i.id === userSession.primaryInstrumentId);
      setSelectedPaymentInstrument(paymentInstrument);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userSession, userSession?.id]);

  useEffect(() => {
    if (userMembershipsQuery.data?.length > 0) {
      const membership = userMembershipsQuery.data.find(membership => membership.status === 'Active');

      setUserHasMembership(Boolean(membership));

      const parseMembershipDefinition = marketPlaceQuery.data?.membershipDefinitions.find(
        membershipDefinition => membershipDefinition.id === membership?.membershipDefinitionId
      );

      setSelectedMembership(parseMembershipDefinition);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userMembershipsQuery.dataUpdatedAt]);

  const checkoutData = useRef({
    gratuities: [
      {
        id: 101,
        value: 10,
        selected: true
      },
      {
        id: 102,
        value: 15,
        selected: false
      },
      {
        id: 103,
        value: 20,
        selected: false
      }
    ]
  });

  const [block, element] = bemify('checkout-summary');

  if (checkoutSummaryQuery.isLoading || !checkoutSummaryQuery.data) {
    return (
      <div className={block()}>
        <div className={element('summary')}>
          <div className={element('loading')}>
            <SpinnerLoader
              show={true}
              useOverlay={false}
              classes={element('spinner')}
            />
          </div>
        </div>
      </div>
    );
  }

  if (checkoutSummaryQuery.isError) {
    return (
      <div className={block()}>
        <div className={element('summary')}>
          <div className={element('error')}>
            Unable to calculate checkout summary. Please try again or contact support.
          </div>
        </div>
      </div>
    );
  }

  // Extract data from the checkoutSummary resolver
  const summaryData = checkoutSummaryQuery.data;
  const membership = summaryData.membership / 100;
  const credit = summaryData.credit[0] / 100;
  const discount = summaryData.discount[0] / 100;
  const gratuity = summaryData.gratuity / 100;
  const travelFee = summaryData.travelFee[0] / 100;
  const total = summaryData.total[0] / 100;

  // Process values for display (show positive values for discounts, hide zeros)
  const processedSummary = {
    membership: membership > 0 ? membership : 0,
    credit: credit < 0 ? Math.abs(credit) : 0,
    discount: discount < 0 ? Math.abs(discount) : 0,
    gratuity,
    travelFee: travelFee > 0 ? travelFee : 0,
    total
  };

  // Calculate "Due now" amount based on payment settings
  const calculateDueNow = () => {
    const paymentSettings = marketPlaceQuery.data?.paymentSettings;

    if (!paymentSettings) return total;

    switch (paymentSettings.paymentCollectionMethod) {
      case 'collect_on_site':
        return 0;
      case 'collect_deposit':
        if (paymentSettings.paymentDepositType === 'percentage') {
          return (total * (paymentSettings.paymentDepositValue || 0)) / 100;
        } else if (paymentSettings.paymentDepositType === 'fixed_amount') {
          return paymentSettings.paymentDepositValue || 0;
        }
        return total;
      case 'collect_on_confirmation':
      default:
        return total;
    }
  };

  const dueNow = calculateDueNow();

  // Generate payment caption text
  const getPaymentCaption = () => {
    if (dueNow === 0) {
      return 'Payment will be collected at the time of your appointment.';
    } else if (dueNow < total) {
      return 'Your deposit will be collected when your appointment is confirmed.';
    } else {
      return 'Your card will be charged when your appointment is confirmed.';
    }
  };

  const isLoading = isLoadingMutation(createAppointmentMutation);
  const isFormDisabled = isLoading || checkoutSummaryQuery.isError;

  return (
    <div className={block()}>
      <div className={element('summary')}>
        <SummaryBooking
          appointmentType={appointmentType}
          organization={organizationSelected}
          procedures={procedures}
          location={location}
          appointment={selectedAppointment}
          availabilities={selectedAvailabilities}
        />

        <Divider />

        <SummaryContact
          formSchema={formsSummary.forms.personalInformation}
          data={contactData}
          onFormChange={setContactData}
          onLogged={() => {
            setShowCompleteCheckout(true);
          }}
        />

        {showCompleteCheckout && (
          <React.Fragment>
            <SummaryMembership
              currentMembership={selectedMembership}
              marketplace={marketPlaceQuery.data}
              procedures={procedures}
              memberships={userMembershipsQuery.data}
              onSelectOption={m => {
                setSelectedMembership(m);
              }}
            />

            <Divider />

            <SummaryTotal
              procedures={procedures}
              membership={processedSummary.membership}
              credit={processedSummary.credit}
              discount={processedSummary.discount}
              gratuity={processedSummary.gratuity}
              travelFee={processedSummary.travelFee}
              total={processedSummary.total}
              dueNow={dueNow}
            />

            <SummaryGratuity
              gratuityCost={gratuity}
              gratuitySelected={selectedGratuity}
              options={checkoutData.current.gratuities}
              onSelectOption={value => {
                setSelectedGratuity(prev => {
                  if (prev && prev.id !== 'other' && prev?.id === value.id) {
                    return undefined;
                  }
                  return { ...value };
                });
              }}
            />

            <Divider />

            <SummaryPayment
              ref={summaryPaymentRef}
              profile={userSession?.profile || contactData}
              paymentInstrument={selectedPaymentInstrument}
              paymentInstruments={userSession?.paymentInstruments}
              onChangeMethod={setSelectedPaymentInstrument}
              onTokenGenerated={setPaymentToken}
              isAuthenticated={Boolean(userSession?.profile)}
              paymentCaption={getPaymentCaption()}
            />

            <SummaryNotes formData={formsSummary.forms.additionalNotes} />

            <Divider />

            <SummaryPaymentPolicy
              paymentSettings={marketPlaceQuery.data?.paymentSettings}
              onAttestationChange={setPaymentPolicyAttested}
              isAttested={paymentPolicyAttested}
            />

            <div className={element('errors')}>
              <ErrorBar errors={errors} />
            </div>

            <div className={element('footer')}>
              <Button
                loading={isLoading}
                disabled={isFormDisabled}
                onClick={async () => {
                  if (isFormDisabled) return;

                  try {
                    setErrors([]);

                    // Check payment policy attestation if required
                    const paymentSettings = marketPlaceQuery.data?.paymentSettings;
                    if (
                      paymentSettings?.hasPaymentPolicy &&
                      paymentSettings?.requirePaymentPolicyAttestation &&
                      !paymentPolicyAttested
                    ) {
                      setErrors(['You must agree to the payment policy to continue.']);
                      return;
                    }

                    let currentPaymentToken = paymentToken;

                    if (!selectedPaymentInstrument && !paymentToken) {
                      if (summaryPaymentRef.current) {
                        try {
                          const tokenData = await summaryPaymentRef.current.generateToken();
                          currentPaymentToken = tokenData;
                        } catch (error) {
                          setErrors([
                            error.message ||
                              'Failed to process payment information. Please check your card details and try again.'
                          ]);
                          return;
                        }
                      } else {
                        setErrors(['Payment form not ready. Please try again.']);
                        return;
                      }
                    }

                    const { notes } = getFormData(formsSummary.forms.additionalNotes);

                    const dateRanges = parseAvailabilityData(procedures, selectedAppointment, selectedAvailabilities);

                    if (!selectedPaymentInstrument && !currentPaymentToken) {
                      setErrors(['No payment method available']);
                      return;
                    }

                    createAppointmentMutation.mutate({
                      selectedOrgId: organizationSelected?.id,
                      membershipDefinitionId: !userHasMembership ? selectedMembership?.id : null,
                      paymentMethod: selectedPaymentInstrument?.id
                        ? {
                            paymentInstrumentId: selectedPaymentInstrument.id
                          }
                        : currentPaymentToken
                        ? {
                            card: {
                              token: currentPaymentToken.token,
                              identity: currentPaymentToken.identity
                            }
                          }
                        : {},
                      procedures,
                      dateRanges,
                      gratuity: calculateGratuityAmount(),
                      notes,
                      registerUser: !userSession?.profile
                        ? {
                            email: contactData?.email,
                            profile: {
                              givenName: contactData?.givenName,
                              familyName: contactData?.familyName,
                              phone: contactData?.phone,
                              dob: contactData?.dob,
                              tzid: Intl.DateTimeFormat().resolvedOptions().timeZone
                            },
                            optedIn: true
                          }
                        : undefined
                    });
                  } catch (error) {
                    setErrors([error.message || 'An unexpected error occurred. Please try again.']);
                  }
                }}>
                Submit
              </Button>
            </div>
          </React.Fragment>
        )}
      </div>
    </div>
  );
};

export default CheckoutSummary;
